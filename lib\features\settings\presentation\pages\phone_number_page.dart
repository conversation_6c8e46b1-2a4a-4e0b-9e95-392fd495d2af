import 'package:flutter/material.dart';
import '../../data/services/phone_number_service.dart';
import '../../domain/models/phone_number_model.dart';

class PhoneNumberPage extends StatefulWidget {
  const PhoneNumberPage({super.key});

  @override
  State<PhoneNumberPage> createState() => _PhoneNumberPageState();
}

class _PhoneNumberPageState extends State<PhoneNumberPage> {
  final _formKey = GlobalKey<FormState>();

  final TextEditingController _bkashController = TextEditingController();
  final TextEditingController _nagadController = TextEditingController();
  final TextEditingController _rocketController = TextEditingController();
  final TextEditingController _upayController = TextEditingController();

  final PhoneNumberService _phoneNumberService = PhoneNumberService();

  bool _isLoading = true;
  bool _isSubmitting = false;
  String? _id;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _fetchPhoneNumbers();
  }

  @override
  void dispose() {
    _bkashController.dispose();
    _nagadController.dispose();
    _rocketController.dispose();
    _upayController.dispose();
    super.dispose();
  }

  Future<void> _fetchPhoneNumbers() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final phoneNumbers = await _phoneNumberService.getAllPhoneNumbers();

      if (phoneNumbers.isNotEmpty) {
        final firstNumber = phoneNumbers.first;

        setState(() {
          _id = firstNumber.id;
          _bkashController.text = firstNumber.bkash;
          _nagadController.text = firstNumber.nagad;
          _rocketController.text = firstNumber.rocket;
          _upayController.text = firstNumber.upay;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to load phone numbers: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _savePhoneNumbers() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isSubmitting = true;
      _errorMessage = null;
    });

    try {
      final phoneNumber = PhoneNumberModel(
        id: _id,
        bkash: _bkashController.text,
        nagad: _nagadController.text,
        rocket: _rocketController.text,
        upay: _upayController.text,
      );

      bool success;

      if (_id != null) {
        // Update existing record
        success = await _phoneNumberService.updatePhoneNumber(phoneNumber);
      } else {
        // Insert new record
        success = await _phoneNumberService.addPhoneNumber(phoneNumber);
      }

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Phone numbers saved successfully'),
            backgroundColor: Colors.green,
          ),
        );

        if (_id == null) {
          // If it was a new record, fetch to get the ID
          _fetchPhoneNumbers();
        }
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isSubmitting = false;
      });
    }
  }

  String? _validatePhoneNumber(String? value) {
    if (value == null || value.isEmpty) {
      return 'This field is required';
    }

    // Basic phone number validation - adjust as needed for your requirements
    if (!RegExp(r'^\d{10,15}$').hasMatch(value)) {
      return 'Please enter a valid phone number';
    }

    return null;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Payment Phone Numbers'), elevation: 0),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Mobile Payment Numbers',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Add or update your mobile payment service numbers',
                      style: Theme.of(
                        context,
                      ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
                    ),
                    const SizedBox(height: 24),

                    if (_errorMessage != null)
                      Container(
                        padding: const EdgeInsets.all(12),
                        margin: const EdgeInsets.only(bottom: 16),
                        decoration: BoxDecoration(
                          color: Colors.red.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.red.shade200),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.error_outline, color: Colors.red),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                _errorMessage!,
                                style: TextStyle(color: Colors.red.shade800),
                              ),
                            ),
                          ],
                        ),
                      ),

                    Form(
                      key: _formKey,
                      child: Card(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: [
                              _buildPaymentField(
                                controller: _bkashController,
                                label: 'bKash Number',
                                icon: 'assets/icons/bkash.png',
                                color: Colors.pink,
                              ),
                              const SizedBox(height: 16),

                              _buildPaymentField(
                                controller: _nagadController,
                                label: 'Nagad Number',
                                icon: 'assets/icons/nagad.png',
                                color: Colors.orange,
                              ),
                              const SizedBox(height: 16),

                              _buildPaymentField(
                                controller: _rocketController,
                                label: 'Rocket Number',
                                icon: 'assets/icons/rocket.png',
                                color: Colors.purple,
                              ),
                              const SizedBox(height: 16),

                              _buildPaymentField(
                                controller: _upayController,
                                label: 'Upay Number',
                                icon: 'assets/icons/upay.png',
                                color: Colors.blue,
                              ),
                              const SizedBox(height: 24),

                              ElevatedButton(
                                onPressed:
                                    _isSubmitting ? null : _savePhoneNumbers,
                                style: ElevatedButton.styleFrom(
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 16,
                                  ),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                                child:
                                    _isSubmitting
                                        ? const SizedBox(
                                          height: 20,
                                          width: 20,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                            color: Colors.white,
                                          ),
                                        )
                                        : const Text(
                                          'SAVE NUMBERS',
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 16,
                                          ),
                                        ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
    );
  }

  Widget _buildPaymentField({
    required TextEditingController controller,
    required String label,
    required String icon,
    required Color color,
  }) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          child: CircleAvatar(
            backgroundColor: color.withOpacity(0.1),
            radius: 16,
            child: Icon(Icons.phone_android, color: color, size: 18),
            // In a real app, you would use the actual logo:
            // child: Image.asset(icon, width: 20, height: 20),
          ),
        ),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
      ),
      keyboardType: TextInputType.phone,
      validator: _validatePhoneNumber,
    );
  }
}
