import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_wallet_admin/core/routes/app_routes.dart';
import 'package:go_wallet_admin/features/settings/presentation/pages/exchange_rate.dart';

class SettingsPage extends StatelessWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Settings')),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildSecuritySection(context),
          const SizedBox(height: 24),
          _buildNoticeSection(context),
        ],
      ),
    );
  }

  Widget _buildSecuritySection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Security', style: Theme.of(context).textTheme.titleLarge),
        const SizedBox(height: 16),
        Card(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              ListTile(
                leading: const Icon(Icons.currency_exchange),
                title: const Text('Exchange Rate'),
                trailing: const Icon(Icons.chevron_right),
                onTap: () {
                  Get.toNamed(AppRoutes.exchangeRate);
                },
              ),
              const Divider(),
              ListTile(
                leading: const Icon(Icons.phone_android),
                title: const Text('Phone Number'),
                subtitle: const Text('Add or update your phone number'),
                trailing: const Icon(Icons.chevron_right),
                onTap: () {
                  Get.toNamed(AppRoutes.phoneNumber);
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildNoticeSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Management', style: Theme.of(context).textTheme.titleLarge),
        const SizedBox(height: 16),
        Card(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              ListTile(
                leading: const Icon(Icons.priority_high, color: Colors.red),
                title: const Text('Top Notices'),
                subtitle: const Text('Manage important announcements'),
                trailing: const Icon(Icons.chevron_right),
                onTap: () {
                  Get.toNamed(AppRoutes.topNotice);
                },
              ),
              const Divider(),
              ListTile(
                leading: const Icon(Icons.notifications_outlined),
                title: const Text('Notices'),
                subtitle: const Text('Manage system notices'),
                trailing: const Icon(Icons.chevron_right),
                onTap: () {
                  Get.toNamed(AppRoutes.notice);
                },
              ),
              const Divider(),
              ListTile(
                leading: const Icon(Icons.lock_outline),
                title: const Text('Change Password'),
                trailing: const Icon(Icons.chevron_right),
                onTap: () {
                  Get.toNamed(AppRoutes.changePassword);
                },
              ),
            ],
          ),
        ),
      ],
    );
  }
}
