import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../data/services/add_money_service.dart';
import '../../domain/models/add_money_model.dart';

class AddMoneyPage extends StatefulWidget {
  const AddMoneyPage({super.key});

  @override
  State<AddMoneyPage> createState() => _AddMoneyPageState();
}

class _AddMoneyPageState extends State<AddMoneyPage>
    with SingleTickerProviderStateMixin {
  final _addMoneyService = AddMoneyService();
  List<AddMoneyRequest> _requests = [];
  List<AddMoneyRequest> _filteredRequests = [];
  bool _isLoading = true;
  String? _error;

  late TabController _tabController;
  final List<String> _tabs = ['All', 'Pending', 'Accepted', 'Cancelled'];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabs.length, vsync: this);
    _tabController.addListener(_handleTabChange);
    _loadRequests();
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    super.dispose();
  }

  void _handleTabChange() {
    if (!_tabController.indexIsChanging) {
      _filterRequests(_tabController.index);
    }
  }

  void _filterRequests(int tabIndex) {
    setState(() {
      if (tabIndex == 0) {
        // All requests
        _filteredRequests = List.from(_requests);
      } else if (tabIndex == 1) {
        // Pending requests
        _filteredRequests =
            _requests
                .where((req) => req.status == null || req.status == 'pending')
                .toList();
      } else if (tabIndex == 2) {
        // Accepted requests
        _filteredRequests =
            _requests.where((req) => req.status == 'accept').toList();
      } else if (tabIndex == 3) {
        // Cancelled requests
        _filteredRequests =
            _requests.where((req) => req.status == 'canceled').toList();
      }
    });
  }

  Future<void> _loadRequests() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final response = await _addMoneyService.getAllAddMoneyRequests();
      setState(() {
        _requests = response.data;
        _filterRequests(_tabController.index);
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _error = e.toString();
      });
      Get.snackbar(
        'Error',
        'Failed to load add money requests: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  Future<void> _updateStatus(AddMoneyRequest request, String status) async {
    try {
      // Convert amount to double
      final amount = request.amount;
      print(
        'Updating status: account=${request.account}, status=$status, amount=$amount',
      );

      final success = await _addMoneyService.updateAddMoneyStatus(
        request.account,
        status,
        amount: status == 'accept' ? amount : null,
      );

      if (success) {
        Get.snackbar(
          'Success',
          'Request ${status == 'accept' ? 'accepted' : 'cancelled'} successfully',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor:
              status == 'accept'
                  ? Colors.green.shade100
                  : Colors.orange.shade100,
          colorText:
              status == 'accept'
                  ? Colors.green.shade800
                  : Colors.orange.shade800,
          duration: const Duration(seconds: 3),
        );
        _loadRequests(); // Reload the list
      } else {
        Get.snackbar(
          'Error',
          'Failed to update request status',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.shade100,
          colorText: Colors.red.shade800,
        );
      }
    } catch (e) {
      print('Error updating status: $e');
      Get.snackbar(
        'Error',
        'Failed to update request status: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  void _showFullScreenImage(BuildContext context, String imageUrl) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder:
            (context) => Scaffold(
              backgroundColor: Colors.black,
              appBar: AppBar(
                backgroundColor: Colors.black,
                title: const Text(
                  'Transaction Receipt',
                  style: TextStyle(color: Colors.white),
                ),
                leading: IconButton(
                  icon: const Icon(Icons.arrow_back, color: Colors.white),
                  onPressed: () => Navigator.pop(context),
                ),
              ),
              body: Center(
                child: InteractiveViewer(
                  minScale: 0.5,
                  maxScale: 4.0,
                  child: Image.network(
                    imageUrl,
                    fit: BoxFit.contain,
                    loadingBuilder: (context, child, loadingProgress) {
                      if (loadingProgress == null) return child;
                      return Center(
                        child: CircularProgressIndicator(
                          value:
                              loadingProgress.expectedTotalBytes != null
                                  ? loadingProgress.cumulativeBytesLoaded /
                                      loadingProgress.expectedTotalBytes!
                                  : null,
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),
      ),
    );
  }

  Color _getStatusColor(String? status) {
    if (status == null || status == 'pending') {
      return Colors.amber;
    } else if (status == 'accept') {
      return Colors.green;
    } else if (status == 'canceled') {
      return Colors.red;
    }
    return Colors.grey;
  }

  String _getStatusText(String? status) {
    if (status == null) {
      return 'Pending';
    } else if (status == 'accept') {
      return 'Accepted';
    } else if (status == 'canceled') {
      return 'Cancelled';
    }
    return status.capitalize ?? status;
  }

  IconData _getStatusIcon(String? status) {
    if (status == null || status == 'pending') {
      return Icons.pending_outlined;
    } else if (status == 'accept') {
      return Icons.check_circle_outline;
    } else if (status == 'canceled') {
      return Icons.cancel_outlined;
    }
    return Icons.help_outline;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Money Requests'),
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadRequests,
            tooltip: 'Refresh',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs:
              _tabs.map((tab) {
                IconData icon;
                Color color;

                switch (tab) {
                  case 'Pending':
                    icon = Icons.pending_outlined;
                    color = Colors.amber;
                    break;
                  case 'Accepted':
                    icon = Icons.check_circle_outline;
                    color = Colors.green;
                    break;
                  case 'Cancelled':
                    icon = Icons.cancel_outlined;
                    color = Colors.red;
                    break;
                  default:
                    icon = Icons.list_alt;
                    color = Theme.of(context).primaryColor;
                }

                return Tab(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(icon, size: 16, color: color),
                      const SizedBox(width: 4),
                      Text(tab),
                    ],
                  ),
                );
              }).toList(),
          isScrollable: true,
          labelColor: Theme.of(context).primaryColor,
          unselectedLabelColor: Colors.grey,
          indicatorSize: TabBarIndicatorSize.label,
          indicatorWeight: 3,
        ),
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _error != null
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.error_outline, size: 48, color: Colors.red),
                    const SizedBox(height: 16),
                    Text(
                      'Error',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.red.shade700,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _error!,
                      style: const TextStyle(color: Colors.red),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 24),
                    ElevatedButton.icon(
                      onPressed: _loadRequests,
                      icon: const Icon(Icons.refresh),
                      label: const Text('Retry'),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              )
              : _filteredRequests.isEmpty
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.inbox_outlined,
                      size: 64,
                      color: Colors.grey.shade400,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No Requests Found',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey.shade700,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _tabs[_tabController.index] == 'All'
                          ? 'There are no add money requests to display'
                          : 'No ${_tabs[_tabController.index].toLowerCase()} requests found',
                      style: TextStyle(color: Colors.grey.shade600),
                    ),
                  ],
                ),
              )
              : ListView.builder(
                itemCount: _filteredRequests.length,
                padding: const EdgeInsets.all(16),
                itemBuilder: (context, index) {
                  final request = _filteredRequests[index];
                  final statusColor = _getStatusColor(request.status);
                  final statusText = _getStatusText(request.status);
                  final statusIcon = _getStatusIcon(request.status);

                  return Card(
                    margin: const EdgeInsets.only(bottom: 16),
                    elevation: 2,
                    clipBehavior: Clip.antiAlias,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      children: [
                        Container(
                          color: Theme.of(
                            context,
                          ).primaryColor.withOpacity(0.1),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 12,
                          ),
                          child: Row(
                            children: [
                              CircleAvatar(
                                backgroundColor: Theme.of(context).primaryColor,
                                radius: 20,
                                child: Text(
                                  request.account.toString(),
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'User ID: ${request.account}',
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 16,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      'Transaction ID: ${request.transactionId}',
                                      style: TextStyle(
                                        color: Colors.grey.shade700,
                                        fontSize: 13,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 10,
                                  vertical: 5,
                                ),
                                decoration: BoxDecoration(
                                  color: statusColor.withOpacity(0.2),
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: statusColor,
                                    width: 1,
                                  ),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      statusIcon,
                                      size: 16,
                                      color: statusColor,
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      statusText,
                                      style: TextStyle(
                                        color: statusColor,
                                        fontWeight: FontWeight.bold,
                                        fontSize: 12,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.all(16),
                          child: Row(
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    _infoRow(
                                      Icons.attach_money,
                                      'Amount',
                                      '\$${request.amount.toStringAsFixed(2)}',
                                      Colors.green.shade700,
                                    ),
                                    const SizedBox(height: 12),
                                    _infoRow(
                                      Icons.calendar_today,
                                      'Date',
                                      request.timestamp ?? 'N/A',
                                      Colors.blue.shade700,
                                    ),
                                  ],
                                ),
                              ),
                              if (request.status == null ||
                                  request.status == 'pending')
                                IconButton(
                                  icon: const Icon(Icons.more_vert),
                                  onPressed: () {
                                    showModalBottomSheet(
                                      context: context,
                                      shape: const RoundedRectangleBorder(
                                        borderRadius: BorderRadius.vertical(
                                          top: Radius.circular(16),
                                        ),
                                      ),
                                      builder:
                                          (context) => SafeArea(
                                            child: Column(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                Container(
                                                  width: 40,
                                                  height: 4,
                                                  margin:
                                                      const EdgeInsets.symmetric(
                                                        vertical: 8,
                                                      ),
                                                  decoration: BoxDecoration(
                                                    color: Colors.grey.shade300,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                          2,
                                                        ),
                                                  ),
                                                ),
                                                Padding(
                                                  padding: const EdgeInsets.all(
                                                    16.0,
                                                  ),
                                                  child: Text(
                                                    'Transaction Actions',
                                                    style: TextStyle(
                                                      fontSize: 18,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      color:
                                                          Theme.of(
                                                            context,
                                                          ).primaryColor,
                                                    ),
                                                  ),
                                                ),
                                                ListTile(
                                                  leading: Container(
                                                    padding:
                                                        const EdgeInsets.all(8),
                                                    decoration: BoxDecoration(
                                                      color: Colors.green
                                                          .withOpacity(0.2),
                                                      shape: BoxShape.circle,
                                                    ),
                                                    child: const Icon(
                                                      Icons
                                                          .check_circle_outline,
                                                      color: Colors.green,
                                                    ),
                                                  ),
                                                  title: const Text(
                                                    'Accept Request',
                                                  ),
                                                  subtitle: const Text(
                                                    'Add funds to user balance',
                                                  ),
                                                  onTap: () {
                                                    Navigator.pop(context);
                                                    _updateStatus(
                                                      request,
                                                      'accept',
                                                    );
                                                  },
                                                ),
                                                ListTile(
                                                  leading: Container(
                                                    padding:
                                                        const EdgeInsets.all(8),
                                                    decoration: BoxDecoration(
                                                      color: Colors.red
                                                          .withOpacity(0.2),
                                                      shape: BoxShape.circle,
                                                    ),
                                                    child: const Icon(
                                                      Icons.cancel_outlined,
                                                      color: Colors.red,
                                                    ),
                                                  ),
                                                  title: const Text(
                                                    'Cancel Request',
                                                  ),
                                                  subtitle: const Text(
                                                    'Reject this transaction',
                                                  ),
                                                  onTap: () {
                                                    Navigator.pop(context);
                                                    _updateStatus(
                                                      request,
                                                      'canceled',
                                                    );
                                                  },
                                                ),
                                                const SizedBox(height: 16),
                                              ],
                                            ),
                                          ),
                                    );
                                  },
                                ),
                            ],
                          ),
                        ),
                        if (request.imagePath != null)
                          GestureDetector(
                            onTap:
                                () => _showFullScreenImage(
                                  context,
                                  request.imagePath!,
                                ),
                            child: Container(
                              height: 200,
                              width: double.infinity,
                              decoration: BoxDecoration(
                                border: Border(
                                  top: BorderSide(
                                    color: Colors.grey.shade300,
                                    width: 1,
                                  ),
                                ),
                              ),
                              child: Stack(
                                fit: StackFit.expand,
                                children: [
                                  Image.network(
                                    request.imagePath!,
                                    fit: BoxFit.cover,
                                    loadingBuilder: (
                                      context,
                                      child,
                                      loadingProgress,
                                    ) {
                                      if (loadingProgress == null) return child;
                                      return Center(
                                        child: CircularProgressIndicator(
                                          value:
                                              loadingProgress
                                                          .expectedTotalBytes !=
                                                      null
                                                  ? loadingProgress
                                                          .cumulativeBytesLoaded /
                                                      loadingProgress
                                                          .expectedTotalBytes!
                                                  : null,
                                        ),
                                      );
                                    },
                                  ),
                                  Positioned(
                                    right: 8,
                                    bottom: 8,
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 12,
                                        vertical: 6,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.black.withOpacity(0.7),
                                        borderRadius: BorderRadius.circular(16),
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: const [
                                          Icon(
                                            Icons.fullscreen,
                                            color: Colors.white,
                                            size: 16,
                                          ),
                                          SizedBox(width: 4),
                                          Text(
                                            'View Receipt',
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontSize: 12,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                      ],
                    ),
                  );
                },
              ),
    );
  }

  Widget _infoRow(IconData icon, String label, String value, Color iconColor) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, size: 18, color: iconColor),
        const SizedBox(width: 8),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
            ),
            const SizedBox(height: 2),
            Text(
              value,
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 15,
                color: Colors.grey.shade900,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
