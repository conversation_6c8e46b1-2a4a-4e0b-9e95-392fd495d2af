import 'dart:convert';
import 'package:go_wallet_admin/core/api_endpoints.dart';
import 'package:http/http.dart' as http;
import '../../domain/models/phone_number_model.dart';

class PhoneNumberService {
  // Replace with your actual API URL
  static const String baseUrl = ApiEndpoints.phoneNumber;

  // Get all phone numbers
  Future<List<PhoneNumberModel>> getAllPhoneNumbers() async {
    try {
      final response = await http.get(Uri.parse('$baseUrl?action=read_all'));

      final data = json.decode(response.body);

      if (data['status'] == 'success') {
        final List<dynamic> phoneNumbersJson = data['data'];
        return phoneNumbersJson
            .map((json) => PhoneNumberModel.fromJson(json))
            .toList();
      } else {
        throw Exception(data['message'] ?? 'Failed to load phone numbers');
      }
    } catch (e) {
      throw Exception('Error fetching phone numbers: $e');
    }
  }

  // Get a single phone number by ID
  Future<PhoneNumberModel> getPhoneNumberById(String id) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl?action=read_one&id=$id'),
      );

      final data = json.decode(response.body);

      if (data['status'] == 'success') {
        return PhoneNumberModel.fromJson(data['data']);
      } else {
        throw Exception(data['message'] ?? 'Failed to load phone number');
      }
    } catch (e) {
      throw Exception('Error fetching phone number: $e');
    }
  }

  // Add a new phone number
  Future<bool> addPhoneNumber(PhoneNumberModel phoneNumber) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl?action=insert'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(phoneNumber.toJson()),
      );

      final data = json.decode(response.body);

      if (data['status'] == 'success') {
        return true;
      } else {
        throw Exception(data['message'] ?? 'Failed to add phone number');
      }
    } catch (e) {
      throw Exception('Error adding phone number: $e');
    }
  }

  // Update an existing phone number
  Future<bool> updatePhoneNumber(PhoneNumberModel phoneNumber) async {
    if (phoneNumber.id == null) {
      throw Exception('Phone number ID is required for update');
    }

    try {
      final response = await http.post(
        Uri.parse('$baseUrl?action=update'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(phoneNumber.toJson()),
      );

      final data = json.decode(response.body);

      if (data['status'] == 'success') {
        return true;
      } else {
        throw Exception(data['message'] ?? 'Failed to update phone number');
      }
    } catch (e) {
      throw Exception('Error updating phone number: $e');
    }
  }

  // Delete a phone number
  Future<bool> deletePhoneNumber(String id) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl?action=delete'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({'id': id}),
      );

      final data = json.decode(response.body);

      if (data['status'] == 'success') {
        return true;
      } else {
        throw Exception(data['message'] ?? 'Failed to delete phone number');
      }
    } catch (e) {
      throw Exception('Error deleting phone number: $e');
    }
  }
}
