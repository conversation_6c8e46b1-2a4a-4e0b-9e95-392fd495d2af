import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../data/services/top_notice_service.dart';
import '../../domain/models/top_notice_model.dart';
import 'add_edit_top_notice_page.dart';

class TopNoticePage extends StatefulWidget {
  const TopNoticePage({super.key});

  @override
  State<TopNoticePage> createState() => _TopNoticePageState();
}

class _TopNoticePageState extends State<TopNoticePage> {
  final TopNoticeService _topNoticeService = TopNoticeService();
  bool _isLoading = true;
  String? _errorMessage;
  List<TopNoticeModel> _topNotices = [];

  @override
  void initState() {
    super.initState();
    _fetchTopNotices();
  }

  Future<void> _fetchTopNotices() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final notices = await _topNoticeService.getAllTopNotices();
      setState(() {
        _topNotices = notices;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to load top notices: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  Future<void> _deleteTopNotice(TopNoticeModel notice) async {
    if (notice.id == null) return;

    try {
      final success = await _topNoticeService.deleteTopNotice(notice.id!);
      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Top notice deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );
        _fetchTopNotices(); // Refresh the list
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to delete top notice: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Top Notices'), elevation: 0),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const AddEditTopNoticePage(),
            ),
          );
          if (result == true && mounted) {
            _fetchTopNotices();
          }
        },
        child: const Icon(Icons.add),
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _errorMessage != null
              ? _buildErrorView()
              : _topNotices.isEmpty
              ? _buildEmptyView()
              : _buildTopNoticesList(),
    );
  }

  Widget _buildErrorView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, color: Colors.red, size: 60),
            const SizedBox(height: 16),
            Text('Error', style: Theme.of(context).textTheme.titleLarge),
            const SizedBox(height: 8),
            Text(
              _errorMessage ?? 'Something went wrong',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _fetchTopNotices,
              child: const Text('Try Again'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.notifications_off_outlined,
              color: Colors.grey,
              size: 60,
            ),
            const SizedBox(height: 16),
            Text(
              'No Top Notices',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            const Text(
              'There are no top notices available at the moment.',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () async {
                final result = await Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const AddEditTopNoticePage(),
                  ),
                );
                if (result == true && mounted) {
                  _fetchTopNotices();
                }
              },
              child: const Text('Add Top Notice'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopNoticesList() {
    return RefreshIndicator(
      onRefresh: _fetchTopNotices,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _topNotices.length,
        itemBuilder: (context, index) {
          final notice = _topNotices[index];
          return _buildTopNoticeCard(notice);
        },
      ),
    );
  }

  Widget _buildTopNoticeCard(TopNoticeModel notice) {
    String formattedDate = '';
    if (notice.timestamp != null) {
      try {
        final date = DateTime.parse(notice.timestamp!);
        formattedDate = DateFormat('MMM d, yyyy • h:mm a').format(date);
      } catch (e) {
        formattedDate = notice.timestamp ?? '';
      }
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with actions
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Icon(Icons.priority_high, color: Colors.red),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Top Notice',
                        style: Theme.of(
                          context,
                        ).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.red,
                        ),
                      ),
                      if (formattedDate.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.only(top: 4),
                          child: Text(
                            formattedDate,
                            style: Theme.of(context).textTheme.bodySmall
                                ?.copyWith(color: Colors.grey[600]),
                          ),
                        ),
                    ],
                  ),
                ),
                PopupMenuButton<String>(
                  onSelected: (value) async {
                    if (value == 'edit') {
                      final result = await Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder:
                              (context) => AddEditTopNoticePage(notice: notice),
                        ),
                      );
                      if (result == true && mounted) {
                        _fetchTopNotices();
                      }
                    } else if (value == 'delete') {
                      _showDeleteConfirmationDialog(notice);
                    }
                  },
                  itemBuilder:
                      (context) => [
                        const PopupMenuItem<String>(
                          value: 'edit',
                          child: Row(
                            children: [
                              Icon(Icons.edit_outlined),
                              SizedBox(width: 8),
                              Text('Edit'),
                            ],
                          ),
                        ),
                        const PopupMenuItem<String>(
                          value: 'delete',
                          child: Row(
                            children: [
                              Icon(Icons.delete_outline, color: Colors.red),
                              SizedBox(width: 8),
                              Text(
                                'Delete',
                                style: TextStyle(color: Colors.red),
                              ),
                            ],
                          ),
                        ),
                      ],
                ),
              ],
            ),
          ),
          // Divider
          const Divider(height: 1),
          // Notice content
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.red.shade50,
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(12),
                bottomRight: Radius.circular(12),
              ),
            ),
            child: Text(
              notice.notice,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _showDeleteConfirmationDialog(TopNoticeModel notice) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Delete Top Notice'),
          content: SingleChildScrollView(
            child: ListBody(
              children: <Widget>[
                const Text('Are you sure you want to delete this top notice?'),
                const SizedBox(height: 8),
                Text(
                  notice.notice,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('Cancel'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: const Text('Delete', style: TextStyle(color: Colors.red)),
              onPressed: () {
                Navigator.of(context).pop();
                _deleteTopNotice(notice);
              },
            ),
          ],
        );
      },
    );
  }
}
