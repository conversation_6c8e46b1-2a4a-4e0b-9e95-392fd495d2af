class AddMoneyRequest {
  final int id;
  final int account;
  final String transactionId;
  final double amount;
  final String? imagePath;
  final String? status;
  final String? timestamp;

  AddMoneyRequest({
    required this.id,
    required this.account,
    required this.transactionId,
    required this.amount,
    this.imagePath,
    this.status,
    this.timestamp,
  });

  factory AddMoneyRequest.fromJson(Map<String, dynamic> json) {
    return AddMoneyRequest(
      id: int.parse(json['id'].toString()),
      account: int.parse(json['account'].toString()),
      transactionId: json['transaction_id'] ?? '',
      amount: double.parse(json['amount'].toString()),
      imagePath: json['image_path']?.toString(),
      status: json['status']?.toString(),
      timestamp: json['timestamp']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'account': account,
      'transaction_id': transactionId,
      'amount': amount,
      'image_path': imagePath,
      'status': status,
      'timestamp': timestamp,
    };
  }
}

class AddMoneyResponse {
  final String status;
  final List<AddMoneyRequest> data;

  AddMoneyResponse({required this.status, required this.data});

  factory AddMoneyResponse.fromJson(Map<String, dynamic> json) {
    return AddMoneyResponse(
      status: json['status'] ?? 'error',
      data:
          (json['data'] as List?)
              ?.map((item) => AddMoneyRequest.fromJson(item))
              .toList() ??
          [],
    );
  }
}
