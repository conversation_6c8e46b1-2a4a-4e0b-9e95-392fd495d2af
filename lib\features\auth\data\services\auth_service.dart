import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../../domain/models/login_model.dart';

class AuthService {
  static const String _keyIsLoggedIn = 'isLoggedIn';
  static const String _keyAdminId = 'adminId';
  static const String _keyAdminPhone = 'adminPhone';
  static const String _keyAdminData = 'adminData';

  // Save login information
  Future<void> saveLoginInfo(LoginResponse response) async {
    if (!response.success || response.adminId == null) {
      return;
    }

    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_keyIsLoggedIn, true);
    await prefs.setString(_keyAdminId, response.adminId!);
    
    if (response.phone != null) {
      await prefs.setString(_keyAdminPhone, response.phone!);
    }

    // Save the full response as JSON for future use
    final responseMap = {
      'success': response.success,
      'message': response.message,
      'admin_id': response.adminId,
      'phone': response.phone,
    };
    await prefs.setString(_keyAdminData, jsonEncode(responseMap));
  }

  // Check if user is logged in
  Future<bool> isLoggedIn() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_keyIsLoggedIn) ?? false;
  }

  // Get admin ID
  Future<String?> getAdminId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_keyAdminId);
  }

  // Get admin phone
  Future<String?> getAdminPhone() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_keyAdminPhone);
  }

  // Get all admin data
  Future<Map<String, dynamic>?> getAdminData() async {
    final prefs = await SharedPreferences.getInstance();
    final adminDataString = prefs.getString(_keyAdminData);
    
    if (adminDataString == null) {
      return null;
    }
    
    try {
      return jsonDecode(adminDataString) as Map<String, dynamic>;
    } catch (e) {
      print('Error parsing admin data: $e');
      return null;
    }
  }

  // Logout
  Future<void> logout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_keyIsLoggedIn, false);
    await prefs.remove(_keyAdminId);
    await prefs.remove(_keyAdminPhone);
    await prefs.remove(_keyAdminData);
  }
} 