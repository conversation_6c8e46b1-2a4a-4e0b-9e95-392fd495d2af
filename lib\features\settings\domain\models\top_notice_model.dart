class TopNoticeModel {
  final int? id;
  final String notice;
  final String? timestamp;

  TopNoticeModel({
    this.id,
    required this.notice,
    this.timestamp,
  });

  factory TopNoticeModel.fromJson(Map<String, dynamic> json) {
    return TopNoticeModel(
      id: json['id'] != null ? int.parse(json['id'].toString()) : null,
      notice: json['notice'] ?? '',
      timestamp: json['timestamp'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      'notice': notice,
    };
  }

  TopNoticeModel copyWith({
    int? id,
    String? notice,
    String? timestamp,
  }) {
    return TopNoticeModel(
      id: id ?? this.id,
      notice: notice ?? this.notice,
      timestamp: timestamp ?? this.timestamp,
    );
  }
} 