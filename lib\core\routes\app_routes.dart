import 'package:get/get.dart';
import 'package:go_wallet_admin/features/add_money/presentation/bindings/add_money_binding.dart';
import 'package:go_wallet_admin/features/add_money/presentation/pages/add_money_page.dart';
import 'package:go_wallet_admin/features/settings/presentation/pages/change_password_page.dart';
import 'package:go_wallet_admin/features/settings/presentation/pages/exchange_rate.dart';
import 'package:go_wallet_admin/features/settings/presentation/pages/notice_page.dart';
import 'package:go_wallet_admin/features/settings/presentation/pages/phone_number_page.dart';
import 'package:go_wallet_admin/features/settings/presentation/pages/top_notice_page.dart';
import '../../features/auth/presentation/pages/login_page.dart';
import '../../features/auth/presentation/pages/splash_page.dart';
import '../../features/dashboard/presentation/pages/dashboard_page.dart';
import '../../features/users/presentation/pages/users_page.dart';
import '../../features/transactions/presentation/pages/transactions_page.dart';
import '../../features/settings/presentation/pages/settings_page.dart';

class AppRoutes {
  static const String splash = '/splash';
  static const String login = '/login';
  static const String dashboard = '/dashboard';
  static const String users = '/users';
  static const String transactions = '/transactions';
  static const String settings = '/settings';
  static const String addMoney = '/add-money';
  static const String exchangeRate = '/exchange-rate';
  static const String phoneNumber = '/phone-number';
  static const String changePassword = '/change-password';
  static const String notice = '/notice';
  static const String topNotice = '/top-notice';

  static final List<GetPage> pages = [
    GetPage(
      name: splash,
      page: () => const SplashPage(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: login,
      page: () => const LoginPage(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: dashboard,
      page: () => const DashboardPage(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: users,
      page: () => const UsersPage(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: addMoney,
      page: () => const AddMoneyPage(),
      binding: AddMoneyBinding(),
    ),
    GetPage(
      name: transactions,
      page: () => const TransactionsPage(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: settings,
      page: () => const SettingsPage(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: exchangeRate,
      page: () => const ExchangeRatePage(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: phoneNumber,
      page: () => const PhoneNumberPage(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: changePassword,
      page: () => const ChangePasswordPage(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: notice,
      page: () => const NoticePage(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: topNotice,
      page: () => const TopNoticePage(),
      transition: Transition.fadeIn,
    ),
  ];
}
