class LoginModel {
  final String phone;
  final String password;

  LoginModel({required this.phone, required this.password});

  Map<String, dynamic> toJson() {
    return {'phone': phone, 'password': password};
  }

  factory LoginModel.fromJson(Map<String, dynamic> json) {
    return LoginModel(
      phone: json['phone'] ?? '',
      password: json['password'] ?? '',
    );
  }
}

class LoginResponse {
  final bool success;
  final String? message;
  final String? error;
  final String? adminId;
  final String? phone;

  LoginResponse({
    required this.success,
    this.message,
    this.error,
    this.adminId,
    this.phone,
  });

  factory LoginResponse.fromJson(Map<String, dynamic> json) {
    return LoginResponse(
      success: json['success'] ?? false,
      message: json['message'],
      error: json['error'],
      adminId: json['admin_id']?.toString(),
      phone: json['phone'],
    );
  }
}
