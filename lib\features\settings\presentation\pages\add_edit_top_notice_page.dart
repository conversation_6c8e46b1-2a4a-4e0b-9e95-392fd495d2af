import 'package:flutter/material.dart';
import '../../data/services/top_notice_service.dart';
import '../../domain/models/top_notice_model.dart';

class AddEditTopNoticePage extends StatefulWidget {
  final TopNoticeModel? notice;

  const AddEditTopNoticePage({super.key, this.notice});

  @override
  State<AddEditTopNoticePage> createState() => _AddEditTopNoticePageState();
}

class _AddEditTopNoticePageState extends State<AddEditTopNoticePage> {
  final _formKey = GlobalKey<FormState>();
  
  final TextEditingController _noticeController = TextEditingController();
  
  final TopNoticeService _topNoticeService = TopNoticeService();
  
  bool _isSubmitting = false;
  bool _isEditing = false;
  
  @override
  void initState() {
    super.initState();
    _isEditing = widget.notice != null;
    
    if (_isEditing) {
      _noticeController.text = widget.notice!.notice;
    }
  }
  
  @override
  void dispose() {
    _noticeController.dispose();
    super.dispose();
  }
  
  Future<void> _saveTopNotice() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }
    
    setState(() {
      _isSubmitting = true;
    });
    
    try {
      final notice = TopNoticeModel(
        id: _isEditing ? widget.notice!.id : null,
        notice: _noticeController.text,
      );
      
      bool success;
      if (_isEditing) {
        // We don't have an update endpoint in the API, so we'll delete and create
        // This is a workaround since the API doesn't support updates
        if (notice.id != null) {
          await _topNoticeService.deleteTopNotice(notice.id!);
        }
        success = await _topNoticeService.addTopNotice(notice);
      } else {
        success = await _topNoticeService.addTopNotice(notice);
      }
      
      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_isEditing ? 'Top notice updated successfully' : 'Top notice added successfully'),
            backgroundColor: Colors.green,
          ),
        );
        
        Navigator.pop(context, true); // Return true to indicate success
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save top notice: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'Edit Top Notice' : 'Add Top Notice'),
        elevation: 0,
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // Notice content field
            TextFormField(
              controller: _noticeController,
              decoration: InputDecoration(
                labelText: 'Notice Content',
                hintText: 'Enter top notice content',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                alignLabelWithHint: true,
                prefixIcon: const Padding(
                  padding: EdgeInsets.all(12.0),
                  child: Icon(Icons.priority_high, color: Colors.red),
                ),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter notice content';
                }
                return null;
              },
              maxLines: 5,
              textCapitalization: TextCapitalization.sentences,
            ),
            const SizedBox(height: 24),
            
            // Preview section
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.visibility_outlined, color: Colors.red),
                      const SizedBox(width: 8),
                      Text(
                        'Preview',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.red,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    _noticeController.text.isEmpty ? 'Your top notice will appear here' : _noticeController.text,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: _noticeController.text.isEmpty ? Colors.grey : null,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
            
            // Save button
            ElevatedButton(
              onPressed: _isSubmitting ? null : _saveTopNotice,
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                backgroundColor: Colors.red,
              ),
              child: _isSubmitting
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Colors.white,
                      ),
                    )
                  : Text(
                      _isEditing ? 'UPDATE TOP NOTICE' : 'ADD TOP NOTICE',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
            ),
          ],
        ),
      ),
    );
  }
} 