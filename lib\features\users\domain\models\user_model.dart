class UserModel {
  final String id;
  final String name;
  final String phone;
  final String email;
  final String reffer;
  final String profileImage;
  final String pin;
  final String password;
  final String? balance;

  UserModel({
    required this.id,
    required this.name,
    required this.phone,
    required this.email,
    required this.reffer,
    required this.profileImage,
    required this.pin,
    required this.password,
    this.balance,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'].toString(),
      name: json['name'] ?? '',
      phone: json['phone'] ?? '',
      email: json['email'] ?? '',
      reffer: json['reffer'] ?? '',
      profileImage: json['profile_image'] ?? '',
      pin: json['pin'] ?? '',
      balance: json['balance']?.toString() ?? '0',
      password: json['password'] ?? '',
    );
  }
}

class UsersResponse {
  final bool success;
  final List<UserModel> data;

  UsersResponse({required this.success, required this.data});

  factory UsersResponse.fromJson(Map<String, dynamic> json) {
    return UsersResponse(
      success: json['success'] ?? false,
      data:
          (json['data'] as List)
              .map((user) => UserModel.fromJson(user))
              .toList(),
    );
  }
}
