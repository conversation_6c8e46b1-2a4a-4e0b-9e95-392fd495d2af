import 'dart:convert';
import 'package:go_wallet_admin/core/api_endpoints.dart';
import 'package:http/http.dart' as http;
import '../../domain/models/add_money_model.dart';


class AddMoneyService {
  static  String baseUrl = ApiEndpoints.addMoney;

  Future<AddMoneyResponse> getAllAddMoneyRequests() async {
    try {
      final response = await http.get(Uri.parse(baseUrl));

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        print('API Response: $jsonData');
        return AddMoneyResponse.fromJson(jsonData);
      } else {
        throw Exception('Failed to load add money requests: ${response.body}');
      }
    } catch (e) {
      print('Error in getAllAddMoneyRequests: $e');
      throw Exception('Error: $e');
    }
  }

  Future<bool> updateAddMoneyStatus(
    int account,
    String status, {
    double? amount,
  }) async {
    try {
      print(
        'Updating status for account: $account, status: $status, amount: $amount',
      );

      // Create request body
      final Map<String, dynamic> body = {'account': account, 'status': status};

      // For 'accept' status, we need to include the amount
      if (status == 'accept' && amount != null) {
        // Send amount as a string to match PHP expectation
        body['amount'] = amount.toString();
      }

      print('Request body: $body');

      final response = await http.put(
        Uri.parse(baseUrl),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(body),
      );

      print('Response status code: ${response.statusCode}');
      print('Response body: ${response.body}');

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        return jsonData['status'] == 'success';
      } else {
        print('Error response: ${response.body}');
        throw Exception(
          'Failed to update status: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in updateAddMoneyStatus: $e');
      throw Exception('Error: $e');
    }
  }
}
