import 'dart:convert';
import 'package:http/http.dart' as http;
import '../../domain/models/exchange_rate_model.dart';
import '../../../../core/api_endpoints.dart';

class ExchangeRateService {
  final String baseUrl = ApiEndpoints.exchangeRate;

  Future<List<ExchangeRate>> getAll() async {
    final response = await http.get(Uri.parse(baseUrl));
    final data = jsonDecode(response.body);
    if (data['status'] == 'success') {
      return (data['data'] as List)
          .map((e) => ExchangeRate.fromJson(e))
          .toList();
    } else {
      throw Exception(data['message'] ?? 'Failed to fetch exchange rates');
    }
  }

  Future<ExchangeRate> getByCurrencyCode(String currencyCode) async {
    final response = await http.get(
      Uri.parse('$baseUrl?currency_code=$currencyCode'),
    );
    final data = jsonDecode(response.body);
    if (data['status'] == 'success' && (data['data'] as List).isNotEmpty) {
      return ExchangeRate.fromJson(data['data'][0]);
    } else {
      throw Exception(data['message'] ?? 'Exchange rate not found');
    }
  }

  Future<ExchangeRate> insert({
    required String countryCode,
    required String currencyCode,
    required double rate,
    String baseCurrency = 'USD',
  }) async {
    final response = await http.post(
      Uri.parse(baseUrl),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'country_code': countryCode,
        'currency_code': currencyCode,
        'rate': rate,
        'base_currency': baseCurrency,
      }),
    );
    final data = jsonDecode(response.body);
    if (data['status'] == 'success') {
      return ExchangeRate.fromJson(data['data']);
    } else {
      throw Exception(data['message'] ?? 'Failed to add exchange rate');
    }
  }

  Future<void> update({required int id, required double rate}) async {
    final response = await http.put(
      Uri.parse(baseUrl),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({'id': id, 'rate': rate}),
    );
    final data = jsonDecode(response.body);
    if (data['status'] != 'success') {
      throw Exception(data['message'] ?? 'Failed to update exchange rate');
    }
  }

  Future<void> delete(int id) async {
    final response = await http.delete(
      Uri.parse(baseUrl),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({'id': id}),
    );
    final data = jsonDecode(response.body);
    if (data['status'] != 'success') {
      throw Exception(data['message'] ?? 'Failed to delete exchange rate');
    }
  }
}
