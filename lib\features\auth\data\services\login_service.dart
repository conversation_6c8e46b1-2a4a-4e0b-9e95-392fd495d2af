import 'dart:convert';
import 'package:go_wallet_admin/core/api_endpoints.dart';
import 'package:http/http.dart' as http;
import '../../domain/models/login_model.dart';

class LoginService {
  static const String baseUrl = ApiEndpoints.login;

  Future<LoginResponse> login(LoginModel loginModel) async {
    try {
      // Clean the phone number to ensure it's in the correct format
      final cleanPhone = loginModel.phone.replaceAll(RegExp(r'[^\d]'), '');

      final requestData = {
        'phone': cleanPhone,
        'password': loginModel.password,
      };

      final response = await http.post(
        Uri.parse(baseUrl),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: jsonEncode(requestData),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return LoginResponse.fromJson(data);
      } else {
        return LoginResponse(
          success: false,
          error: 'Server error: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      return LoginResponse(
        success: false,
        error: 'An error occurred: ${e.toString()}',
      );
    }
  }
}
