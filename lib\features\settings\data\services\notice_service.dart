import 'dart:convert';
import 'package:go_wallet_admin/core/api_endpoints.dart';
import 'package:http/http.dart' as http;
import '../../domain/models/notice_model.dart';

class NoticeService {
  // API URL
  static const String baseUrl = ApiEndpoints.allNotice;

  // Get all notices
  Future<List<NoticeModel>> getAllNotices() async {
    try {
      final response = await http.get(Uri.parse(baseUrl));

      final data = json.decode(response.body);

      if (data['status'] == 'success') {
        if (data.containsKey('data')) {
          final List<dynamic> noticesJson = data['data'];
          return noticesJson.map((json) => NoticeModel.fromJson(json)).toList();
        } else {
          // No notices found, return empty list
          return [];
        }
      } else {
        throw Exception(data['message'] ?? 'Failed to load notices');
      }
    } catch (e) {
      throw Exception('Error fetching notices: $e');
    }
  }

  // Add a new notice
  Future<bool> addNotice(NoticeModel notice) async {
    try {
      final response = await http.post(
        Uri.parse(baseUrl),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(notice.toJson()),
      );

      final data = json.decode(response.body);

      if (data['status'] == 'success') {
        return true;
      } else {
        throw Exception(data['message'] ?? 'Failed to add notice');
      }
    } catch (e) {
      throw Exception('Error adding notice: $e');
    }
  }

  // Delete a notice
  Future<bool> deleteNotice(int id) async {
    try {
      final response = await http.delete(Uri.parse('$baseUrl?id=$id'));

      final data = json.decode(response.body);

      if (data['status'] == 'success') {
        return true;
      } else {
        throw Exception(data['message'] ?? 'Failed to delete notice');
      }
    } catch (e) {
      throw Exception('Error deleting notice: $e');
    }
  }
}
