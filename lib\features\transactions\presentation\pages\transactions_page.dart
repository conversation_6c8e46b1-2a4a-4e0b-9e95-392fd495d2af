import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../data/services/banking_service.dart';
import '../../domain/models/banking_model.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class TransactionsPage extends StatefulWidget {
  const TransactionsPage({super.key});

  @override
  State<TransactionsPage> createState() => _TransactionsPageState();
}

class _TransactionsPageState extends State<TransactionsPage> {
  final _bankingService = BankingService();
  List<UserBanking> _allTransactions = [];
  List<UserBanking> _filteredTransactions = [];
  bool _isLoading = true;
  bool _isRefreshing = false;
  String? _error;
  String _selectedFilter = 'All';
  String? _selectedStatus;
  final List<String> _filters = [
    'All',
    'bKash',
    'Nagad',
    'Rocket',
    'Upay',
    'Bank',
  ];
  final List<String> _statusFilters = [
    'All',
    'Active',
    'Pending',
    'Completed',
    'Canceled',
  ];

  @override
  void initState() {
    super.initState();
    _loadTransactions();
  }

  Future<void> _loadTransactions() async {
    try {
      final response = await _bankingService.getAllBanking();
      setState(() {
        _allTransactions = response.data;
        _filterTransactions(_selectedFilter);
        _isLoading = false;
        _isRefreshing = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
        _isRefreshing = false;
      });
      Get.snackbar(
        'Error',
        'Failed to load transactions',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  Future<void> _handleRefresh() async {
    setState(() {
      _isRefreshing = true;
      _error = null;
    });
    await _loadTransactions();
  }

  void _filterTransactions(String filter) {
    setState(() {
      _selectedFilter = filter;
      _applyFilters();
    });
  }

  void _filterByStatus(String? status) {
    setState(() {
      _selectedStatus = status;
      _applyFilters();
    });
  }

  void _applyFilters() {
    var filtered = _allTransactions;

    // Apply type filter (bKash, Nagad, etc.)
    if (_selectedFilter != 'All') {
      if (_selectedFilter == 'Bank') {
        filtered =
            filtered.where((user) => user.bankTransfers.isNotEmpty).toList();
      } else {
        filtered =
            filtered
                .where(
                  (user) => user.mobileBanking.any(
                    (mb) => mb.operator == _selectedFilter,
                  ),
                )
                .toList();
      }
    }

    // Apply status filter
    if (_selectedStatus != null && _selectedStatus != 'All') {
      filtered =
          filtered
              .map((user) {
                final filteredUser = UserBanking(
                  userId: user.userId,
                  name: user.name,
                  profileImage: user.profileImage,
                  mobileBanking:
                      user.mobileBanking.where((mb) {
                        if (_selectedFilter == 'Bank') return false;
                        if (_selectedFilter != 'All' &&
                            mb.operator != _selectedFilter)
                          return false;
                        return _normalizeStatus(mb.status) == _selectedStatus;
                      }).toList(),
                  bankTransfers:
                      user.bankTransfers.where((bt) {
                        if (_selectedFilter != 'Bank' &&
                            _selectedFilter != 'All')
                          return false;
                        return _normalizeStatus(bt.status) == _selectedStatus;
                      }).toList(),
                );
                return filteredUser;
              })
              .where(
                (user) =>
                    user.mobileBanking.isNotEmpty ||
                    user.bankTransfers.isNotEmpty,
              )
              .toList();
    }

    setState(() {
      _filteredTransactions = filtered;
    });
  }

  String _normalizeStatus(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return 'Active';
      case 'pending':
        return 'Pending';
      case 'completed':
        return 'Completed';
      case 'canceled':
      case 'cancelled':
        return 'Canceled';
      default:
        return status;
    }
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Filter by Status'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children:
                          _statusFilters.map((status) {
                            return ChoiceChip(
                              label: Text(status),
                              selected: _selectedStatus == status,
                              onSelected: (selected) {
                                setState(() {
                                  _selectedStatus = selected ? status : null;
                                });
                              },
                            );
                          }).toList(),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    setState(() {
                      _selectedStatus = null;
                    });
                    _applyFilters();
                    Navigator.pop(context);
                  },
                  child: const Text('Clear'),
                ),
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Cancel'),
                ),
                FilledButton(
                  onPressed: () {
                    _applyFilters();
                    Navigator.pop(context);
                  },
                  child: const Text('Apply'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Future<void> _updateTransactionStatus(
    String transactionId,
    String userId,
    String status,
    String amount,
  ) async {
    try {
      final success = await _bankingService.updateTransactionStatus(
        transactionId: transactionId,
        userId: userId,
        newStatus: status,
        amount: amount,
      );

      if (success) {
        Get.snackbar(
          'Success',
          'Transaction ${status == 'accept' ? 'accepted' : 'cancelled'} successfully',
          snackPosition: SnackPosition.BOTTOM,
        );
        // Refresh the transactions list
        await _loadTransactions();
      } else {
        throw Exception('Failed to update transaction status');
      }
    } catch (e) {
      print('Error details: $e'); // Print detailed error for debugging
      Get.snackbar(
        'Error',
        'Failed to update transaction status: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Transactions'),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(60),
          child: Container(
            height: 60,
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              scrollDirection: Axis.horizontal,
              itemCount: _filters.length,
              itemBuilder: (context, index) {
                return Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: FilterChip(
                    label: Text(_filters[index]),
                    selected: _selectedFilter == _filters[index],
                    onSelected: (selected) {
                      _filterTransactions(_filters[index]);
                    },
                  ),
                );
              },
            ),
          ),
        ),
        actions: [
          if (_isRefreshing)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            )
          else
            IconButton(
              icon: const Icon(Icons.filter_list),
              onPressed: _showFilterDialog,
            ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _handleRefresh,
        child:
            _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _error != null
                ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(_error!),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _handleRefresh,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
                : _filteredTransactions.isEmpty
                ? const Center(child: Text('No transactions found'))
                : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: _filteredTransactions.length,
                  itemBuilder: (context, index) {
                    final user = _filteredTransactions[index];
                    return _buildUserTransactionsCard(context, user);
                  },
                ),
      ),
    );
  }

  Widget _buildUserTransactionsCard(BuildContext context, UserBanking user) {
    final List<Widget> transactionCards = [];

    // Group mobile banking transactions by operator
    final Map<String, List<MobileBanking>> operatorTransactions = {};
    for (var mobile in user.mobileBanking) {
      if (_selectedFilter == 'All' || mobile.operator == _selectedFilter) {
        operatorTransactions.putIfAbsent(mobile.operator, () => []).add(mobile);
      }
    }

    // Create a card for each operator's transactions
    operatorTransactions.forEach((operator, transactions) {
      transactionCards.add(
        Card(
          margin: const EdgeInsets.only(bottom: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ListTile(
                leading: CircleAvatar(
                  backgroundImage: NetworkImage(user.profileImage),
                  onBackgroundImageError: (_, __) {
                    // Handle image load error
                  },
                ),
                title: Text(user.name),
                subtitle: Text('User ID: ${user.userId}'),
              ),
              const Divider(),
              ...transactions.map(
                (mobile) =>
                    _buildMobileBankingTile(context, mobile, user.userId),
              ),
            ],
          ),
        ),
      );
    });

    // Add bank transfer transactions if applicable
    if (_selectedFilter == 'All' || _selectedFilter == 'Bank') {
      for (var transfer in user.bankTransfers) {
        transactionCards.add(
          Card(
            margin: const EdgeInsets.only(bottom: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ListTile(
                  leading: CircleAvatar(
                    backgroundImage: NetworkImage(user.profileImage),
                    onBackgroundImageError: (_, __) {
                      // Handle image load error
                    },
                  ),
                  title: Text(user.name),
                  subtitle: Text('User ID: ${user.userId}'),
                ),
                const Divider(),
                _buildBankTransferTile(context, transfer, user.userId),
              ],
            ),
          ),
        );
      }
    }

    return Column(children: transactionCards);
  }

  Widget _buildMobileBankingTile(
    BuildContext context,
    MobileBanking mobile,
    String userId,
  ) {
    final bool isCredit = mobile.type == 'Send Money';
    final statusColor =
        mobile.status == 'active'
            ? Colors.green
            : mobile.status == 'pending'
            ? Colors.orange
            : Colors.red;

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          ListTile(
            leading: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: isCredit ? Colors.green : Colors.red,
                shape: BoxShape.circle,
              ),
              child: Icon(
                isCredit ? Icons.arrow_downward : Icons.arrow_upward,
                color: Colors.white,
              ),
            ),
            title: Text(mobile.operator),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Type: ${mobile.type}'),
                Text('Number: ${mobile.number}'),
                Text('Balance: ${mobile.balance}'),
                Text('ID: ${mobile.id}'),
              ],
            ),
            trailing: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: statusColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    mobile.status.toUpperCase(),
                    style: TextStyle(
                      color: statusColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  DateFormat(
                    'MMM dd, yyyy HH:mm',
                  ).format(DateTime.parse(mobile.timestamp)),
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          ),
          if (mobile.status == 'pending')
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 12),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () async {
                        String cleanBalance = mobile.balance.replaceAll(
                          RegExp(r'[^\d.]'),
                          '',
                        );
                        await _updateTransactionStatus(
                          mobile.id,
                          userId,
                          'accept',
                          cleanBalance,
                        );
                      },
                      icon: const Icon(Icons.check, size: 16),
                      label: const Text('Accept'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 8),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () async {
                        await _updateTransactionStatus(
                          mobile.id,
                          userId,
                          'canceled',
                          '0',
                        );
                      },
                      icon: const Icon(Icons.close, size: 16),
                      label: const Text('Cancel'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 8),
                      ),
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildBankTransferTile(
    BuildContext context,
    BankTransfer transfer,
    String userId,
  ) {
    final statusColor =
        transfer.status == 'completed'
            ? Colors.green
            : transfer.status == 'pending'
            ? Colors.orange
            : Colors.red;

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          ListTile(
            leading: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: Colors.blue,
                shape: BoxShape.circle,
              ),
              child: const Icon(Icons.account_balance, color: Colors.white),
            ),
            title: Text(transfer.bank),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Account: ${transfer.accountNumber}'),
                Text('Amount: ${transfer.amount}'),
                Text('ID: ${transfer.id}'),
              ],
            ),
            trailing: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: statusColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    transfer.status.toUpperCase(),
                    style: TextStyle(
                      color: statusColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  DateFormat(
                    'MMM dd, yyyy HH:mm',
                  ).format(DateTime.parse(transfer.timestamp)),
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          ),
          if (transfer.status == 'pending')
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 12),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () async {
                        String cleanAmount = transfer.amount.replaceAll(
                          RegExp(r'[^\d.]'),
                          '',
                        );
                        await _updateTransactionStatus(
                          transfer.id,
                          userId,
                          'accept',
                          cleanAmount,
                        );
                      },
                      icon: const Icon(Icons.check, size: 16),
                      label: const Text('Accept'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 8),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () async {
                        await _updateTransactionStatus(
                          transfer.id,
                          userId,
                          'canceled',
                          '0',
                        );
                      },
                      icon: const Icon(Icons.close, size: 16),
                      label: const Text('Cancel'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 8),
                      ),
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }
}
