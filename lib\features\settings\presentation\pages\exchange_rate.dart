import 'package:flutter/material.dart';
import 'package:country_currency_pickers/country_pickers.dart';
import 'package:country_currency_pickers/country.dart';

import '../../data/services/exchange_rate_service.dart';
import '../../domain/models/exchange_rate_model.dart';

class ExchangeRatePage extends StatefulWidget {
  const ExchangeRatePage({super.key});

  @override
  State<ExchangeRatePage> createState() => _ExchangeRatePageState();
}

class _ExchangeRatePageState extends State<ExchangeRatePage> {
  final _formKey = GlobalKey<FormState>();
  final _service = ExchangeRateService();

  Country? _selectedCountry;
  final _currencyCodeController = TextEditingController();
  final _rateController = TextEditingController();
  bool _isLoading = false;
  bool _isSubmitting = false;
  String? _errorMessage;
  List<ExchangeRate> _rates = [];
  bool _countryPickerError = false;

  @override
  void initState() {
    super.initState();
    _safeInitialize();
  }

  void _safeInitialize() {
    try {
      _selectedCountry = CountryPickerUtils.getCountryByIsoCode('US');
      if (_selectedCountry != null) {
        _currencyCodeController.text = _selectedCountry!.currencyCode ?? 'USD';
      } else {
        _currencyCodeController.text = 'USD';
      }
    } catch (e) {
      print('Country picker initialization failed: $e');
      _countryPickerError = true;
      _currencyCodeController.text = 'USD';
    }

    // Delay the API call to ensure widget is fully built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _fetchRates();
      }
    });
  }

  Future<void> _fetchRates() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final rates = await _service.getAll();
      if (mounted) {
        setState(() {
          _rates = rates;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load exchange rates: ${e.toString()}';
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _submit() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_currencyCodeController.text.isEmpty) {
      setState(() {
        _errorMessage = 'Please select a currency';
      });
      return;
    }

    setState(() {
      _isSubmitting = true;
      _errorMessage = null;
    });

    try {
      await _service.insert(
        countryCode: _selectedCountry?.isoCode ?? 'US',
        currencyCode: _currencyCodeController.text,
        rate: double.parse(_rateController.text),
      );

      if (mounted) {
        _clearForm();
        _fetchRates();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Exchange rate added successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Error: ${e.toString()}';
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  void _clearForm() {
    _rateController.clear();
    _currencyCodeController.text = 'USD';
    _formKey.currentState?.reset();
  }

  @override
  void dispose() {
    _currencyCodeController.dispose();
    _rateController.dispose();
    super.dispose();
  }

  Future<void> _deleteRate(int id) async {
    try {
      await _service.delete(id);
      if (mounted) {
        _fetchRates();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Exchange rate deleted'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to delete: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Widget _buildCountrySelector() {
    if (_countryPickerError) {
      // Fallback to simple dropdown if country picker fails
      return DropdownButtonFormField<String>(
        value:
            _currencyCodeController.text.isNotEmpty
                ? _currencyCodeController.text
                : 'USD',
        decoration: const InputDecoration(
          labelText: 'Currency',
          border: OutlineInputBorder(),
        ),
        items: const [
          DropdownMenuItem(value: 'USD', child: Text('USD - US Dollar')),
          DropdownMenuItem(value: 'EUR', child: Text('EUR - Euro')),
          DropdownMenuItem(value: 'GBP', child: Text('GBP - British Pound')),
          DropdownMenuItem(value: 'JPY', child: Text('JPY - Japanese Yen')),
          DropdownMenuItem(value: 'BDT', child: Text('BDT - Bangladeshi Taka')),
        ],
        onChanged: (value) {
          if (value != null) {
            setState(() {
              _currencyCodeController.text = value;
            });
          }
        },
      );
    }

    try {
      return Container(
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey),
          borderRadius: BorderRadius.circular(4),
        ),
        child: CountryPickerDropdown(
          initialValue: _selectedCountry?.isoCode ?? 'US',
          itemBuilder:
              (Country country) => Row(
                children: [
                  CountryPickerUtils.getDefaultFlagImage(country),
                  const SizedBox(width: 8.0),
                  Expanded(
                    child: Text(
                      country.name ?? '',
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
          onValuePicked: (Country? country) {
            if (country != null) {
              setState(() {
                _selectedCountry = country;
                _currencyCodeController.text = country.currencyCode ?? '';
              });
            }
          },
        ),
      );
    } catch (e) {
      print('CountryPickerDropdown error: $e');
      _countryPickerError = true;
      return _buildCountrySelector(); // Recursive call to fallback
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Exchange Rates'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _isLoading ? null : _fetchRates,
          ),
        ],
      ),
      body: SafeArea(
        child: RefreshIndicator(
          onRefresh: _fetchRates,
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Error Message
                if (_errorMessage != null)
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    margin: const EdgeInsets.only(bottom: 16),
                    decoration: BoxDecoration(
                      color: Colors.red.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.red.shade200),
                    ),
                    child: Row(
                      children: [
                        const Icon(Icons.error_outline, color: Colors.red),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            _errorMessage!,
                            style: TextStyle(color: Colors.red.shade800),
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.close, color: Colors.red),
                          onPressed: () {
                            setState(() {
                              _errorMessage = null;
                            });
                          },
                        ),
                      ],
                    ),
                  ),

                // Add Exchange Rate Form
                Card(
                  elevation: 4,
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Add Exchange Rate',
                            style: Theme.of(context).textTheme.titleLarge
                                ?.copyWith(fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 16),

                          // Country/Currency Selector
                          const Text('Country/Currency'),
                          const SizedBox(height: 8),
                          _buildCountrySelector(),
                          const SizedBox(height: 16),

                          // Currency Code (read-only)
                          TextFormField(
                            controller: _currencyCodeController,
                            readOnly: true,
                            decoration: const InputDecoration(
                              labelText: 'Currency Code',
                              border: OutlineInputBorder(),
                              prefixIcon: Icon(Icons.monetization_on),
                            ),
                          ),
                          const SizedBox(height: 16),

                          // Rate
                          TextFormField(
                            controller: _rateController,
                            decoration: const InputDecoration(
                              labelText: 'Exchange Rate',
                              border: OutlineInputBorder(),
                              prefixIcon: Icon(Icons.trending_up),
                              hintText: 'Enter rate (e.g., 1.25)',
                            ),
                            keyboardType: const TextInputType.numberWithOptions(
                              decimal: true,
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter exchange rate';
                              }
                              final rate = double.tryParse(value);
                              if (rate == null || rate <= 0) {
                                return 'Please enter a valid positive number';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 20),

                          // Submit Button
                          SizedBox(
                            width: double.infinity,
                            height: 50,
                            child: ElevatedButton(
                              onPressed: _isSubmitting ? null : _submit,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Theme.of(context).primaryColor,
                                foregroundColor: Colors.white,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              child:
                                  _isSubmitting
                                      ? const SizedBox(
                                        height: 20,
                                        width: 20,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          color: Colors.white,
                                        ),
                                      )
                                      : const Text(
                                        'ADD EXCHANGE RATE',
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

                const SizedBox(height: 24),

                // Exchange Rates List Header
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'All Exchange Rates',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (_rates.isNotEmpty)
                      Text(
                        '${_rates.length} rates',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 16),

                // Loading or Content
                if (_isLoading)
                  const Center(
                    child: Padding(
                      padding: EdgeInsets.all(32),
                      child: CircularProgressIndicator(),
                    ),
                  )
                else if (_rates.isEmpty)
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(32),
                      child: Center(
                        child: Column(
                          children: [
                            Icon(
                              Icons.currency_exchange,
                              size: 48,
                              color: Colors.grey[400],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'No exchange rates found',
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Add your first exchange rate above',
                              style: TextStyle(color: Colors.grey[600]),
                            ),
                          ],
                        ),
                      ),
                    ),
                  )
                else
                  // Exchange Rates List
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: _rates.length,
                    itemBuilder: (context, index) {
                      final rate = _rates[index];
                      Country? country;

                      if (!_countryPickerError) {
                        try {
                          country = CountryPickerUtils.getCountryByIsoCode(
                            rate.countryCode,
                          );
                        } catch (e) {
                          // Handle country not found
                        }
                      }

                      return Card(
                        margin: const EdgeInsets.only(bottom: 8),
                        elevation: 2,
                        child: ListTile(
                          leading:
                              country != null && !_countryPickerError
                                  ? CountryPickerUtils.getDefaultFlagImage(
                                    country,
                                  )
                                  : CircleAvatar(
                                    backgroundColor:
                                        Theme.of(context).primaryColor,
                                    child: Text(
                                      rate.currencyCode.substring(0, 2),
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                          title: Text(
                            country?.name ?? '${rate.countryCode} Country',
                            style: const TextStyle(fontWeight: FontWeight.w500),
                          ),
                          subtitle: Text(
                            '${rate.currencyCode} - Rate: ${rate.rate.toStringAsFixed(4)}',
                            style: TextStyle(color: Colors.grey[600]),
                          ),
                          trailing: IconButton(
                            icon: const Icon(Icons.delete, color: Colors.red),
                            onPressed: () => _showDeleteConfirmation(rate),
                          ),
                        ),
                      );
                    },
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showDeleteConfirmation(ExchangeRate rate) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Exchange Rate'),
            content: Text(
              'Are you sure you want to delete ${rate.currencyCode} rate?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  _deleteRate(rate.id);
                },
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('Delete'),
              ),
            ],
          ),
    );
  }
}
