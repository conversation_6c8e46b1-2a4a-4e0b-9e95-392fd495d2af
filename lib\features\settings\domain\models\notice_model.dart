class NoticeModel {
  final int? id;
  final String title;
  final String notice;
  final String? timestamp;

  NoticeModel({
    this.id,
    required this.title,
    required this.notice,
    this.timestamp,
  });

  factory NoticeModel.fromJson(Map<String, dynamic> json) {
    return NoticeModel(
      id: json['id'] != null ? int.parse(json['id'].toString()) : null,
      title: json['title'] ?? '',
      notice: json['notice'] ?? '',
      timestamp: json['timestamp'],
    );
  }

  Map<String, dynamic> toJson() {
    return {if (id != null) 'id': id, 'title': title, 'notice': notice};
  }

  NoticeModel copyWith({
    int? id,
    String? title,
    String? notice,
    String? timestamp,
  }) {
    return NoticeModel(
      id: id ?? this.id,
      title: title ?? this.title,
      notice: notice ?? this.notice,
      timestamp: timestamp ?? this.timestamp,
    );
  }
}
