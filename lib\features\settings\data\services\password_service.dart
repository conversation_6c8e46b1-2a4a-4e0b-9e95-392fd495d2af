import 'dart:convert';
import 'package:go_wallet_admin/core/api_endpoints.dart';
import 'package:http/http.dart' as http;

class PasswordService {
  // API URL
  static const String baseUrl = ApiEndpoints.passwordChange;

  // Change password
  Future<bool> changePassword({
    required String phone,
    required String newPassword,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl?action=change_password'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'phone': phone,
          'password': newPassword,
        }),
      );

      final data = json.decode(response.body);

      if (data['status'] == 'success') {
        return true;
      } else {
        throw Exception(data['message'] ?? 'Failed to change password');
      }
    } catch (e) {
      throw Exception('Error changing password: $e');
    }
  }
} 