class PhoneNumberModel {
  final String? id;
  final String bkash;
  final String nagad;
  final String rocket;
  final String upay;

  PhoneNumberModel({
    this.id,
    required this.bkash,
    required this.nagad,
    required this.rocket,
    required this.upay,
  });

  factory PhoneNumberModel.fromJson(Map<String, dynamic> json) {
    return PhoneNumberModel(
      id: json['id']?.toString(),
      bkash: json['bkash'] ?? '',
      nagad: json['nagad'] ?? '',
      rocket: json['rocket'] ?? '',
      upay: json['upay'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      'bkash': bkash,
      'nagad': nagad,
      'rocket': rocket,
      'upay': upay,
    };
  }
} 