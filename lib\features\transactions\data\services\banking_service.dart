import 'dart:convert';
import 'package:go_wallet_admin/core/api_endpoints.dart';
import 'package:http/http.dart' as http;
import '../../domain/models/banking_model.dart';

class BankingService {
  static const String _baseUrl = ApiEndpoints.baseUrl;

  Future<BankingResponse> getAllBanking() async {
    try {
      final response = await http.get(Uri.parse('$_baseUrl/admin/all_banking.php'));

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        return BankingResponse.fromJson(jsonData);
      } else {
        throw Exception('Failed to load banking data: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to load banking data: $e');
    }
  }

  Future<UserMobileBankingResponse> getUserMobileBanking(String userId) async {
    try {
      final response = await http.get(
        Uri.parse(
          '$_baseUrl/mobile_banking/mobile_banking.php?user_id=$userId',
        ),
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        return UserMobileBankingResponse.fromJson(jsonData);
      } else {
        throw Exception(
          'Failed to load user mobile banking data: ${response.statusCode}',
        );
      }
    } catch (e) {
      throw Exception('Failed to load user mobile banking data: $e');
    }
  }

  Future<bool> updateTransactionStatus({
    required String transactionId,
    required String userId,
    required String newStatus,
    required String amount,
  }) async {
    try {
      final response = await http.put(
        Uri.parse('$_baseUrl/mobile_banking/mobile_banking.php'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'id': transactionId,
          'user_id': userId,
          'status': newStatus,
          'amount': amount,
        }),
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        return jsonData['status'] == 'success';
      } else {
        return false;
      }
    } catch (e) {
      throw Exception('Failed to update transaction status: $e');
    }
  }
}
