class ExchangeRate {
  final int id;
  final String countryCode;
  final String currencyCode;
  final double rate;
  final String baseCurrency;

  ExchangeRate({
    required this.id,
    required this.countryCode,
    required this.currencyCode,
    required this.rate,
    required this.baseCurrency,
  });

  factory ExchangeRate.fromJson(Map<String, dynamic> json) {
    return ExchangeRate(
      id:
          json['id'] is int
              ? json['id']
              : int.tryParse(json['id'].toString()) ?? 0,
      countryCode: json['country_code'] ?? '',
      currencyCode: json['currency_code'] ?? '',
      rate:
          json['rate'] is double
              ? json['rate']
              : double.tryParse(json['rate'].toString()) ?? 0.0,
      baseCurrency: json['base_currency'] ?? 'USD',
    );
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'country_code': countryCode,
    'currency_code': currencyCode,
    'rate': rate,
    'base_currency': baseCurrency,
  };
}
