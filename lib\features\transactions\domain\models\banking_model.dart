class MobileBanking {
  final String id;
  final String operator;
  final String type;
  final String number;
  final String balance;
  final String status;
  final String timestamp;

  MobileBanking({
    required this.id,
    required this.operator,
    required this.type,
    required this.number,
    required this.balance,
    required this.status,
    required this.timestamp,
  });

  factory MobileBanking.fromJson(Map<String, dynamic> json) {
    return MobileBanking(
      id: json['mobile_banking_id'] ?? '',
      operator: json['operator'] ?? '',
      type: json['type'] ?? '',
      number: json['number'] ?? '',
      balance: json['mobile_balance'] ?? '0',
      status: json['mobile_status'] ?? '',
      timestamp: json['mobile_timestamp'] ?? '',
    );
  }
}

class BankTransfer {
  final String id;
  final String bank;
  final String accountNumber;
  final String amount;
  final String status;
  final String timestamp;

  BankTransfer({
    required this.id,
    required this.bank,
    required this.accountNumber,
    required this.amount,
    required this.status,
    required this.timestamp,
  });

  factory BankTransfer.fromJson(Map<String, dynamic> json) {
    return BankTransfer(
      id: json['bank_transfer_id'] ?? '',
      bank: json['selected_bank'] ?? '',
      accountNumber: json['account_number'] ?? '',
      amount: json['amount'] ?? '0',
      status: json['bank_status'] ?? '',
      timestamp: json['bank_timestamp'] ?? '',
    );
  }
}

class UserBanking {
  final String userId;
  final String name;
  final String profileImage;
  final List<MobileBanking> mobileBanking;
  final List<BankTransfer> bankTransfers;

  UserBanking({
    required this.userId,
    required this.name,
    required this.profileImage,
    required this.mobileBanking,
    required this.bankTransfers,
  });

  factory UserBanking.fromJson(Map<String, dynamic> json) {
    return UserBanking(
      userId: json['user_id'] ?? '',
      name: json['name'] ?? '',
      profileImage: json['profile_image'] ?? '',
      mobileBanking:
          (json['mobile_banking'] as List<dynamic>?)
              ?.map((e) => MobileBanking.fromJson(e))
              .toList() ??
          [],
      bankTransfers:
          (json['bank_transfer'] as List<dynamic>?)
              ?.map((e) => BankTransfer.fromJson(e))
              .toList() ??
          [],
    );
  }
}

class UserMobileBankingTransaction {
  final String id;
  final String userId;
  final String operator;
  final String type;
  final String number;
  final String balance;
  final String status;
  final String timestamp;

  UserMobileBankingTransaction({
    required this.id,
    required this.userId,
    required this.operator,
    required this.type,
    required this.number,
    required this.balance,
    required this.status,
    required this.timestamp,
  });

  factory UserMobileBankingTransaction.fromJson(Map<String, dynamic> json) {
    return UserMobileBankingTransaction(
      id: json['id']?.toString() ?? '',
      userId: json['user_id']?.toString() ?? '',
      operator: json['operator'] ?? '',
      type: json['type'] ?? '',
      number: json['number'] ?? '',
      balance: json['balance'] ?? '0',
      status: json['status'] ?? '',
      timestamp: json['timestamp'] ?? '',
    );
  }
}

class UserMobileBankingResponse {
  final String status;
  final List<UserMobileBankingTransaction> data;

  UserMobileBankingResponse({required this.status, required this.data});

  factory UserMobileBankingResponse.fromJson(Map<String, dynamic> json) {
    return UserMobileBankingResponse(
      status: json['status'] ?? '',
      data:
          (json['data'] as List<dynamic>?)
              ?.map((e) => UserMobileBankingTransaction.fromJson(e))
              .toList() ??
          [],
    );
  }
}

class BankingResponse {
  final bool success;
  final List<UserBanking> data;

  BankingResponse({required this.success, required this.data});

  factory BankingResponse.fromJson(Map<String, dynamic> json) {
    return BankingResponse(
      success: json['success'] ?? false,
      data:
          (json['data'] as List<dynamic>?)
              ?.map((e) => UserBanking.fromJson(e))
              .toList() ??
          [],
    );
  }
}
