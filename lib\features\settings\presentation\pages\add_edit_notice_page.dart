import 'package:flutter/material.dart';
import '../../data/services/notice_service.dart';
import '../../domain/models/notice_model.dart';

class AddEditNoticePage extends StatefulWidget {
  final NoticeModel? notice;

  const AddEditNoticePage({super.key, this.notice});

  @override
  State<AddEditNoticePage> createState() => _AddEditNoticePageState();
}

class _AddEditNoticePageState extends State<AddEditNoticePage> {
  final _formKey = GlobalKey<FormState>();
  
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _noticeController = TextEditingController();
  
  final NoticeService _noticeService = NoticeService();
  
  bool _isSubmitting = false;
  bool _isEditing = false;
  
  @override
  void initState() {
    super.initState();
    _isEditing = widget.notice != null;
    
    if (_isEditing) {
      _titleController.text = widget.notice!.title;
      _noticeController.text = widget.notice!.notice;
    }
  }
  
  @override
  void dispose() {
    _titleController.dispose();
    _noticeController.dispose();
    super.dispose();
  }
  
  Future<void> _saveNotice() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }
    
    setState(() {
      _isSubmitting = true;
    });
    
    try {
      final notice = NoticeModel(
        id: _isEditing ? widget.notice!.id : null,
        title: _titleController.text,
        notice: _noticeController.text,
      );
      
      bool success;
      if (_isEditing) {
        // We don't have an update endpoint in the API, so we'll delete and create
        // This is a workaround since the API doesn't support updates
        if (notice.id != null) {
          await _noticeService.deleteNotice(notice.id!);
        }
        success = await _noticeService.addNotice(notice);
      } else {
        success = await _noticeService.addNotice(notice);
      }
      
      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_isEditing ? 'Notice updated successfully' : 'Notice added successfully'),
            backgroundColor: Colors.green,
          ),
        );
        
        Navigator.pop(context, true); // Return true to indicate success
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save notice: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'Edit Notice' : 'Add Notice'),
        elevation: 0,
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // Title field
            TextFormField(
              controller: _titleController,
              decoration: InputDecoration(
                labelText: 'Title',
                hintText: 'Enter notice title',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                prefixIcon: const Icon(Icons.title),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a title';
                }
                return null;
              },
              textCapitalization: TextCapitalization.sentences,
            ),
            const SizedBox(height: 16),
            
            // Notice content field
            TextFormField(
              controller: _noticeController,
              decoration: InputDecoration(
                labelText: 'Notice Content',
                hintText: 'Enter notice content',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                alignLabelWithHint: true,
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter notice content';
                }
                return null;
              },
              maxLines: 10,
              textCapitalization: TextCapitalization.sentences,
            ),
            const SizedBox(height: 24),
            
            // Save button
            ElevatedButton(
              onPressed: _isSubmitting ? null : _saveNotice,
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: _isSubmitting
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Colors.white,
                      ),
                    )
                  : Text(
                      _isEditing ? 'UPDATE NOTICE' : 'ADD NOTICE',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
            ),
          ],
        ),
      ),
    );
  }
} 