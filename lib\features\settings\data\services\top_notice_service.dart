import 'dart:convert';
import 'package:go_wallet_admin/core/api_endpoints.dart';
import 'package:http/http.dart' as http;
import '../../domain/models/top_notice_model.dart';

class TopNoticeService {
  // API URL
  static const String baseUrl = ApiEndpoints.topNotice;
  // Get all top notices
  Future<List<TopNoticeModel>> getAllTopNotices() async {
    try {
      final response = await http.get(Uri.parse(baseUrl));

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((json) => TopNoticeModel.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load top notices');
      }
    } catch (e) {
      throw Exception('Error fetching top notices: $e');
    }
  }

  // Add a new top notice
  Future<bool> addTopNotice(TopNoticeModel notice) async {
    try {
      final response = await http.post(
        Uri.parse(baseUrl),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(notice.toJson()),
      );

      final data = json.decode(response.body);

      if (response.statusCode == 200 && !data.containsKey('error')) {
        return true;
      } else {
        throw Exception(data['error'] ?? 'Failed to add top notice');
      }
    } catch (e) {
      throw Exception('Error adding top notice: $e');
    }
  }

  // Delete a top notice
  Future<bool> deleteTopNotice(int id) async {
    try {
      final response = await http.delete(
        Uri.parse(baseUrl),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({'id': id}),
      );

      final data = json.decode(response.body);

      if (response.statusCode == 200 && !data.containsKey('error')) {
        return true;
      } else {
        throw Exception(data['error'] ?? 'Failed to delete top notice');
      }
    } catch (e) {
      throw Exception('Error deleting top notice: $e');
    }
  }
} 